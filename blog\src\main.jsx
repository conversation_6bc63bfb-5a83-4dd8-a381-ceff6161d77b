import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from 'react-router-dom'

import App from './App.jsx'
import { Provider } from 'react-redux'
import { store } from './app/store'

const root = ReactDOM.createRoot(document.getElementById('root'))
root.render(
  <React.StrictMode>
    <BrowserRouter>
      <Provider store={store}>
        <App />
      </Provider>
    </BrowserRouter>
  </React.StrictMode>
)
