import React, { useState } from 'react'
import { useDispatch } from 'react-redux'
import { deleteBlog, updateBlog } from '../features/blog/blogSlice'
import '../style/card.css'

function Card({ id, title, description, date }) {
    const dispatch = useDispatch()
    const [isEditing, setIsEditing] = useState(false)
    const [editTitle, setEditTitle] = useState(title)
    const [editDescription, setEditDescription] = useState(description)

    const handleDelete = () => {
        if (window.confirm('Are you sure you want to delete this blog?')) {
            dispatch(deleteBlog(id))
        }
    }

    const handleEdit = () => {
        setIsEditing(true)
    }

    const handleSave = () => {
        if (editTitle.trim() && editDescription.trim()) {
            dispatch(updateBlog({ 
                id, 
                title: editTitle, 
                description: editDescription,
                date: date // Preserve the original date
            }))
            setIsEditing(false)
        } else {
            alert('Title and description cannot be empty')
        }
    }

    const handleCancel = () => {
        setEditTitle(title)
        setEditDescription(description)
        setIsEditing(false)
    }

    return (
        <div className="card">
            {isEditing ? (
                <div className="card-edit-form">
                    <input
                        type="text"
                        value={editTitle}
                        onChange={(e) => setEditTitle(e.target.value)}
                        className="edit-title"
                        placeholder="Enter title"
                    />
                    <textarea
                        value={editDescription}
                        onChange={(e) => setEditDescription(e.target.value)}
                        className="edit-description"
                        placeholder="Enter description"
                    />
                    <div className="card-actions">
                        <button className="card-btn save-btn" onClick={handleSave}>Save</button>
                        <button className="card-btn cancel-btn" onClick={handleCancel}>Cancel</button>
                    </div>
                </div>
            ) : (
                <>
                    <div className="card-header">
                        <h3>{title}</h3>
                        {date && <span className="date">{date}</span>}
                    </div>
                    <p className="card-description">{description}</p>
                    <div className="card-actions">
                        <button className="card-btn edit-btn" onClick={handleEdit}>Edit</button>
                        <button className="card-btn delete-btn" onClick={handleDelete}>Delete</button>
                    </div>
                </>
            )}
        </div>
    )
}

export default Card
