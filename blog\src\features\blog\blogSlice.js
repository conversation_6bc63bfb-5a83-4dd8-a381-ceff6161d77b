// src/features/blog/blogSlice.js
import { createSlice, nanoid } from '@reduxjs/toolkit';

// Sample initial blogs
const initialBlogs = [
  {
    id: '1',
    title: 'Getting Started with React',
    description: 'React is a JavaScript library for building user interfaces. It allows you to create reusable UI components and efficiently update the DOM when your data changes.',
    date: new Date('2023-05-15').toISOString()
  }
 
];

const blogSlice = createSlice({
  name: 'blog',
  initialState: {
    blogs: initialBlogs,
  },
  reducers: {
    addBlog: (state, action) => { 
      const newBlog = {
        id: nanoid(),
        title: action.payload.title,
        description: action.payload.description,
        date: new Date().toISOString(),
      };
      state.blogs.push(newBlog);
    },
    deleteBlog: (state, action) => {
      state.blogs = state.blogs.filter(blog => blog.id !== action.payload);
    },
    updateBlog: (state, action) => {
      const updatedBlog = action.payload;
      const blogIndex = state.blogs.findIndex(blog => blog.id === updatedBlog.id);
      if (blogIndex !== -1) {
        state.blogs[blogIndex] = updatedBlog;
      }
    }
  },
});

export const { addBlog, deleteBlog, updateBlog } = blogSlice.actions;
export default blogSlice.reducer;
