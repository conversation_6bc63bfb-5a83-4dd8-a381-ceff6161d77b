.home-container {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.home-title {
  font-size: 2rem;
  color: #333;
  margin: 0;
}

.create-blog-btn {
  background-color: #3f51b5;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s;
}

.create-blog-btn:hover {
  background-color: #303f9f;
}

.empty-blogs {
  text-align: center;
  margin: 80px 0;
  color: #666;
  font-size: 1.2rem;
}

.blogs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

@media (max-width: 768px) {
  .home-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .blogs-grid {
    grid-template-columns: 1fr;
  }
}
