import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import Card from '../components/cards';
import { Link } from 'react-router-dom';
import '../style/home.css';

function Home() {
  const blogs = useSelector((state) => state.blog.blogs);

  // For debugging - log the blogs array to console
  useEffect(() => {
    console.log('Blogs from Redux store:', blogs);
  }, [blogs]);

  return (
    <div className="home-container">
      <div className="home-header">
        <h2 className="home-title">All Blogs</h2>
        <Link to="/create" className="create-blog-btn">
          Create New Blog
        </Link>
      </div>

      {blogs.length === 0 ? (
        <div className="empty-blogs">
          <p>No blogs yet. Create your first blog!</p>
        </div>
      ) : (
        <div className="blogs-grid">
          {blogs.map((blog, index) => (
            <Card
              key={blog.id || index}
              id={blog.id}
              title={blog.title}
              description={blog.description}
              date={blog.date ? new Date(blog.date).toLocaleDateString() : ''}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default Home;
