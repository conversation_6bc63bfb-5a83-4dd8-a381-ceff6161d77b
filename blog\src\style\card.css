.card {
    width: 1200px;

    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.card h3 {
    margin: 0;
    color: #333;
    font-size: 1.4rem;
}

.card .date {
    font-size: 0.85rem;
    color: #888;
    font-style: italic;
}

.card p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

.card-edit-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
}

.edit-title {
  font-size: 1.2rem;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.edit-description {
  min-height: 100px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
}
.card-btn{
    background-color: #3f51b5;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    margin: 5px;

}

.save-btn {
  background-color: #4caf50;
}

.save-btn:hover {
  background-color: #45a049;
}

.cancel-btn {
  background-color: #f44336;
}

.cancel-btn:hover {
  background-color: #d32f2f;
}
