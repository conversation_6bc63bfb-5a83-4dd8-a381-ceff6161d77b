import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { addBlog } from '../features/blog/blogSlice';
import { useNavigate } from 'react-router-dom';
import '../style/createblog.css';

function CreateBlog() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(addBlog({ title, description }));
    setTitle('');
    setDescription('');
    // Redirect to home page after creating a blog
    navigate('/');
  };

  return (
    <div className="createblog">
      <h1>Create a New Blog</h1>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={title}
          placeholder="Title"
          onChange={(e) => setTitle(e.target.value)}
          required
        />
        <textarea
          value={description}
          placeholder="Description"
          onChange={(e) => setDescription(e.target.value)}
          required
        />
        <button type="submit">Add Blog</button>
      </form>
    </div>
  );
}

export default CreateBlog;
